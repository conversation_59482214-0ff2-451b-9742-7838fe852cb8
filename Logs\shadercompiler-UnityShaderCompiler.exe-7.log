Base path: 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data', plugins path 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=4104 file=Packages/com.unity.render-pipelines.universal/Shaders/Terrain/TerrainLitAdd.shader name=Hidden/Universal Render Pipeline/Terrain/Lit (Add Pass) pass=TerrainAddLit ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=_MAIN_LIGHT_SHADOWS_CASCADE _LIGHT_LAYERS _FORWARD_PLUS _TERRAIN_INSTANCED_PERPIXEL_NORMAL _ADDITIONAL_LIGHT_SHADOWS _REFLECTION_PROBE_BLENDING _SHADOWS_SOFT _SCREEN_SPACE_OCCLUSION dKW=_ALPHATEST_ON _SHADOWS_SOFT_LOW _SHADOWS_SOFT_MEDIUM _SHADOWS_SOFT_HIGH _LIGHT_COOKIES DEBUG_DISPLAY _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_SCREEN _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS LIGHTMAP_SHADOW_MIXING SHADOWS_SHADOWMASK DIRLIGHTMAP_COMBINED LIGHTMAP_ON FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _TERRAIN_BLEND_HEIGHT _MASKMAP _NORMALMAP UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Fragment platform=d3d11 reqs=227 mask=6 start=54 ok=1 outsize=35694

