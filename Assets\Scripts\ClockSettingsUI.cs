using UnityEngine;
using TMPro;
using System;
using UnityEngine.UI;
using System.Linq;

public class ClockSettingsUI : MonoBehaviour
{
  public CalendarUI calendar;
  private const int minuteStep = 10;

  [SerializeField] private TMP_Dropdown hourDropdown;
  [SerializeField] private TMP_Dropdown minuteDropdown;
  [SerializeField] private TextMeshProUGUI errorText;


  void Start()
  {
    // Stunden 00–23
    hourDropdown.ClearOptions();
    hourDropdown.AddOptions(
        Enumerable.Range(0, 24)
                  .Select(i => i.ToString("D2"))
                  .ToList()
    );

    // Minuten in Schritten
    minuteDropdown.ClearOptions();
    minuteDropdown.AddOptions(
        Enumerable.Range(0, 60 / minuteStep)
                  .Select(i => (i * minuteStep).ToString("D2"))
                  .ToList()
    );
  }

  public void OnConfirm()
  {
    if (errorText != null)
      errorText.text = "";
    try
    {
      DateTime date = calendar.SelectedDate;
      int h = hourDropdown.value;
      int min = minuteDropdown.value * minuteStep;

      DateTime sim = new DateTime(date.Year, date.Month, date.Day, h, min, 0);
      DateTime now = DateTime.Now;

      if (sim > now)
      {
        string warn = $"Ausgewählte Zeit ({sim:dd.MM.yyyy HH:mm}) liegt in der Zukunft. Setze auf aktuelle Zeit ({now:dd.MM.yyyy HH:mm}).";
        Debug.LogWarning(warn);

        if (errorText != null)
        {
          errorText.text = warn;
          errorText.color = Color.yellow;
        }


        // a) Kalender auf Heute stellen
        calendar.SetDate(sim.Date);

        // b) Dropdowns zurücksetzen
        hourDropdown.value = sim.Hour;
        minuteDropdown.value = sim.Minute / minuteStep;
        hourDropdown.RefreshShownValue();
        minuteDropdown.RefreshShownValue();

        sim = now;
      }

      TimeManager.Instance.SetSimulatedTime(sim);
      Debug.Log("Simulierte Zeit gesetzt: " + sim);
    }
    catch (Exception e)
    {
      string msg = $"Ungültige Eingabe: {e.Message}";
      Debug.LogError(msg);

      if (errorText != null)
        errorText.text = msg;
      errorText.color = Color.yellow;
    }
  }

  public void OnResetRealTime()
  {
    TimeManager.Instance.ResetToRealTime();
  }
  
  private void ShowError(string msg)
    {
        if (errorText == null) return;

        errorText.text = msg;

        // alte Routine beenden, falls sie noch läuft
        if (clearErrorRoutine != null)
            StopCoroutine(clearErrorRoutine);

        clearErrorRoutine = StartCoroutine(ClearErrorAfterSeconds(5f));
    }

    private IEnumerator ClearErrorAfterSeconds(float seconds)
    {
        yield return new WaitForSeconds(seconds);
        if (errorText != null)
            errorText.text = "";
        clearErrorRoutine = null;
    }
}
}
