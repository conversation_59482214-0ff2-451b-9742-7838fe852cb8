Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.40f1 (157d81624ddf) revision 1408385'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'de' Physical Memory: 32508 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.40f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
-logFile
Logs/AssetImportWorker1.log
-srvPort
57088
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34736]  Target information:

Player connection [34736]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 2836767613 [EditorId] 2836767613 [Version] 1048832 [Id] WindowsEditor(7,TobiPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34736] Host joined multi-casting on [***********:54997]...
Player connection [34736] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 849.09 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.40f1 (157d81624ddf)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 (ID=0x2216)
    Vendor:   NVIDIA
    VRAM:     10053 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56344
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002416 seconds.
- Loaded All Assemblies, in  0.439 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.219 seconds
Domain Reload Profiling: 656ms
	BeginReloadAssembly (222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (149ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (147ms)
			TypeCache.Refresh (145ms)
				TypeCache.ScanAssembly (135ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (219ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (190ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.531 seconds
Refreshing native plugins compatible for Editor in 5.47 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.953 seconds
Domain Reload Profiling: 2482ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (20ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (1365ms)
		LoadAssemblies (1256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (173ms)
			TypeCache.Refresh (125ms)
				TypeCache.ScanAssembly (111ms)
			BuildScriptInfoCaches (37ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (953ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (197ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 9.54 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 231 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6948 unused Assets / (4.0 MB). Loaded Objects now: 7643.
Memory consumption went from 183.3 MB to 179.2 MB.
Total: 10.905900 ms (FindLiveObjects: 0.750200 ms CreateObjectMapping: 0.648600 ms MarkObjects: 6.357000 ms  DeleteObjects: 3.148400 ms)

========================================================================
Received Import Request.
  Time since last request: 351352.627527 seconds.
  path: Assets/Scripts/NewBrush 1.brush
  artifactKey: Guid(636a8d136222738438764b6c2b3ca7e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/NewBrush 1.brush using Guid(636a8d136222738438764b6c2b3ca7e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3fbe8994c51ae22bb1952d4b479cd976') in 0.088749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Scripts/NewLayer 1.terrainlayer
  artifactKey: Guid(e9aabc93ee82e4f478bd552391dc5d76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/NewLayer 1.terrainlayer using Guid(e9aabc93ee82e4f478bd552391dc5d76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff857df7ebc9e9c73d0d17c23be4db99') in 0.0212123 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1641.287344 seconds.
  path: Assets/Material/TobisBricks/UniWandNormalMap.png
  artifactKey: Guid(934e636df47cada43ba0625178a6eb9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/TobisBricks/UniWandNormalMap.png using Guid(934e636df47cada43ba0625178a6eb9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2dd3951d5568e7f9dadab29d4241bccd') in 0.0289556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 869.106581 seconds.
  path: Assets/Material/TobisBricks/wall_outside_normal.jpg
  artifactKey: Guid(6523affde29470246983dbfd3ececc8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/TobisBricks/wall_outside_normal.jpg using Guid(6523affde29470246983dbfd3ececc8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ca9f08a8d3b992c921d8c369c341798') in 0.0447944 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.209698 seconds.
  path: Assets/Material/TobisBricks/wall_inside_normal.png
  artifactKey: Guid(b0f94c493619ad4468fbedad43666563) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/TobisBricks/wall_inside_normal.png using Guid(b0f94c493619ad4468fbedad43666563) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '17c567036978fde7b2841078ff29cc17') in 0.0180163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 39.837705 seconds.
  path: Assets/Models/rober-h/textures/ARARAT_Normal_2K.jpeg
  artifactKey: Guid(1624ffbdb2b62d047bf604fc1d1b914e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/ARARAT_Normal_2K.jpeg using Guid(1624ffbdb2b62d047bf604fc1d1b914e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9303d8011eb05a47d8ee09bd43b26495') in 0.0236761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Models/rober-h/textures/AvatarEyes_Color_512.jpeg
  artifactKey: Guid(bf7a61f91b403bc4087180be286ecff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarEyes_Color_512.jpeg using Guid(bf7a61f91b403bc4087180be286ecff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '198b203add483d95d13eaf98417b5566') in 0.0155784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Models/rober-h/textures/AvatarHeadMale_Normal1_2K.jpeg
  artifactKey: Guid(8fabb6c0326b1f64589cb6ba9e598ee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarHeadMale_Normal1_2K.jpeg using Guid(8fabb6c0326b1f64589cb6ba9e598ee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c5f9bf571d5b64b6323e68c6c56681f') in 0.0107202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Models/rober-h/textures/AvatarEyes_Normal_512.jpeg
  artifactKey: Guid(039d85f556caf974097fc0195f6918d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarEyes_Normal_512.jpeg using Guid(039d85f556caf974097fc0195f6918d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd6cb7c980885f0a3417f73afb826f267') in 0.016031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Models/rober-h/textures/AvatarTeeth_Normal1_1K.jpeg
  artifactKey: Guid(3bfb44a1cc0ed67478bf3b319346df06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarTeeth_Normal1_1K.jpeg using Guid(3bfb44a1cc0ed67478bf3b319346df06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9229ae829be71d1f1653262835b08fb5') in 0.0113885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Models/rober-h/textures/AvatarHeadMale_Color_2K.jpeg
  artifactKey: Guid(6e3b2e9297ee987408730c0f49d07b66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarHeadMale_Color_2K.jpeg using Guid(6e3b2e9297ee987408730c0f49d07b66) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '96d6ae043fa832a0adee93afa8410877') in 0.0113606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0