Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.40f1 (157d81624ddf) revision 1408385'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'de' Physical Memory: 32508 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.40f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
-logFile
Logs/AssetImportWorker2.log
-srvPort
57088
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [10308]  Target information:

Player connection [10308]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 2925157877 [EditorId] 2925157877 [Version] 1048832 [Id] WindowsEditor(7,TobiPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10308] Host joined multi-casting on [***********:54997]...
Player connection [10308] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 849.00 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.40f1 (157d81624ddf)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 (ID=0x2216)
    Vendor:   NVIDIA
    VRAM:     10053 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56604
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002688 seconds.
- Loaded All Assemblies, in  0.439 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.219 seconds
Domain Reload Profiling: 656ms
	BeginReloadAssembly (222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (149ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (147ms)
			TypeCache.Refresh (146ms)
				TypeCache.ScanAssembly (134ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (219ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (190ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.541 seconds
Refreshing native plugins compatible for Editor in 5.49 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.952 seconds
Domain Reload Profiling: 2491ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (20ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (1372ms)
		LoadAssemblies (1260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (112ms)
			BuildScriptInfoCaches (38ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (953ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (628ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (350ms)
			ProcessInitializeOnLoadMethodAttributes (197ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 9.53 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 231 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6948 unused Assets / (4.1 MB). Loaded Objects now: 7643.
Memory consumption went from 183.1 MB to 179.0 MB.
Total: 11.441100 ms (FindLiveObjects: 0.996900 ms CreateObjectMapping: 0.910900 ms MarkObjects: 6.359900 ms  DeleteObjects: 3.171900 ms)

========================================================================
Received Import Request.
  Time since last request: 351352.628019 seconds.
  path: Assets/Scripts/NewLayer 2.terrainlayer
  artifactKey: Guid(9482f9e861ffb7049a92524199badb9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/NewLayer 2.terrainlayer using Guid(9482f9e861ffb7049a92524199badb9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f72b79b24693b12610bb9b3ab431f6b') in 0.0878556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Scripts/NewBrush.brush
  artifactKey: Guid(3331a622929d6414a906b84763e1824d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/NewBrush.brush using Guid(3331a622929d6414a906b84763e1824d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0062d46df3c36b7a5b8b097f04b49b81') in 0.0211615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1641.286296 seconds.
  path: Assets/Material/TobisBricks/UniWand2.png
  artifactKey: Guid(66f4d8d6f81bc8540a581bb87174a843) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/TobisBricks/UniWand2.png using Guid(66f4d8d6f81bc8540a581bb87174a843) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '650ec5dcbfe1da94c5862dc45f7516f1') in 0.0343036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 71.379041 seconds.
  path: Assets/Scripts/ClockDisplay.cs
  artifactKey: Guid(e3607ae955891524d82cdae2711fde56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/ClockDisplay.cs using Guid(e3607ae955891524d82cdae2711fde56) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18422253513b2fbd48c7cdf752e4492b') in 0.000564 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.949321 seconds.
  path: Assets/Scripts/FollowCanvas.cs
  artifactKey: Guid(6050eb80f85cd48028917b9ea82a6c89) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/FollowCanvas.cs using Guid(6050eb80f85cd48028917b9ea82a6c89) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e4778e7c7a6b0c76b2e1a7913fcd771') in 0.0003926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.541122 seconds.
  path: Assets/Scripts/LoadLatestThermostatReading1.cs
  artifactKey: Guid(3ff8d346dc473f34992d796aac6f699d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LoadLatestThermostatReading1.cs using Guid(3ff8d346dc473f34992d796aac6f699d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f08aa9b84f9dec49278a2235852afd1') in 0.0003692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.255274 seconds.
  path: Assets/Scripts/LookAtCamera.cs
  artifactKey: Guid(56b4214087f2447689c08d391c96d469) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LookAtCamera.cs using Guid(56b4214087f2447689c08d391c96d469) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b6251f86cf5c8d2151beaa789c757c7') in 0.0003765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.328663 seconds.
  path: Assets/Scripts/SunRise.cs
  artifactKey: Guid(6ba00e946fb38794a80fa59b9dec3704) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SunRise.cs using Guid(6ba00e946fb38794a80fa59b9dec3704) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b6187d1f6b9c791282252e5954dc01b') in 0.0003745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 769.270996 seconds.
  path: Assets/Shaders
  artifactKey: Guid(9a81610a56dab7e42a4dda8b6e2cc364) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders using Guid(9a81610a56dab7e42a4dda8b6e2cc364) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f3cf7bcdcd2a7b63702151cbbd5ba82') in 0.0011404 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 26.268925 seconds.
  path: Assets/Material/TobisBricks/wall_outside.jpg
  artifactKey: Guid(3ce878a4ef593fd4385a6971bb678055) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/TobisBricks/wall_outside.jpg using Guid(3ce878a4ef593fd4385a6971bb678055) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a0a58cf67547efe0a000d802d849a64') in 0.0446958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.210464 seconds.
  path: Assets/Material/TobisBricks/wall_inside.jpg
  artifactKey: Guid(27b7c79ad214ee94ab00844e811703d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/TobisBricks/wall_inside.jpg using Guid(27b7c79ad214ee94ab00844e811703d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce0fed8c4bf409bba78773e6903c6135') in 0.017816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 14.055045 seconds.
  path: Assets/Shaders/floor_normalmap.png
  artifactKey: Guid(0e6889e68f4d2f244b4114f9b5ab6c60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders/floor_normalmap.png using Guid(0e6889e68f4d2f244b4114f9b5ab6c60) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2b01f0aa24460f1560796fe84ead2bc') in 0.0132661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 24.483780 seconds.
  path: Assets/Models/rober-h
  artifactKey: Guid(f7542cd0fe56a64439b2d0dd2d23c5c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h using Guid(f7542cd0fe56a64439b2d0dd2d23c5c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d7c2bd4b3f7879e0004885a31dd7085') in 0.000556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.820390 seconds.
  path: Assets/Models/rober-h/textures
  artifactKey: Guid(2421c27ba210c774b9cbc775e51d8bb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures using Guid(2421c27ba210c774b9cbc775e51d8bb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a1971322a9c9fd49c16d6189a92a424b') in 0.0004267 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.050450 seconds.
  path: Assets/Models/rober-h/textures/ARARAT_Color_2K.jpeg
  artifactKey: Guid(df4c50bea26f3f5448fc9d88ff57b6ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/ARARAT_Color_2K.jpeg using Guid(df4c50bea26f3f5448fc9d88ff57b6ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6413a078f56b980aa1705899e77587b') in 0.0252121 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Models/rober-h/textures/AvatarLeftCornea_Color_512.jpeg
  artifactKey: Guid(daba5a41d834e354bba08e9371961078) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarLeftCornea_Color_512.jpeg using Guid(daba5a41d834e354bba08e9371961078) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5a811c48136a69d2ea4f6592bf87d866') in 0.0167152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Models/rober-h/textures/AvatarBodyMale_Normal1_2K.jpeg
  artifactKey: Guid(c84a46816e3c3ad458616f19c35b7118) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarBodyMale_Normal1_2K.jpeg using Guid(c84a46816e3c3ad458616f19c35b7118) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'defe0b12a8499ec688366b184c885ceb') in 0.011496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000010 seconds.
  path: Assets/Models/rober-h/textures/AvatarTeeth_Color_1K.jpeg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarTeeth_Color_1K.jpeg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '088f831e241892d7193f7f4e75c37ea1') in 0.0159567 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Models/rober-h/textures/AvatarRightCornea_Color_512.jpeg
  artifactKey: Guid(3fb0692c5ccd12f448ecc28605c6fc9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarRightCornea_Color_512.jpeg using Guid(3fb0692c5ccd12f448ecc28605c6fc9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8d581b8b8190d975d8a8087ab7753d9') in 0.0113958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Models/rober-h/textures/AvatarBodyMale_Color_2K.jpeg
  artifactKey: Guid(ca18ccf1e94ae8044929a7d0497f13c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/textures/AvatarBodyMale_Color_2K.jpeg using Guid(ca18ccf1e94ae8044929a7d0497f13c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d9f67870ac7f645f84b51757071bb3e') in 0.0106842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 14.763331 seconds.
  path: Assets/Models/rober-h/source
  artifactKey: Guid(e5869c424518cb44da51fa629b7a2ebd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/source using Guid(e5869c424518cb44da51fa629b7a2ebd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92a1aac268f8fa4ca1f072236da52dd5') in 0.0004506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.642550 seconds.
  path: Assets/Models/rober-h/source/habeck.zip
  artifactKey: Guid(f6da6a8cad8706240bcea12c1d393e17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/source/habeck.zip using Guid(f6da6a8cad8706240bcea12c1d393e17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72f9397c3663efa5aa874326e46fa994') in 0.0010221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 137.397391 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Anton SDF - Drop Shadow.mat
  artifactKey: Guid(749b9069dc4742c5bfa5c74644049926) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Anton SDF - Drop Shadow.mat using Guid(749b9069dc4742c5bfa5c74644049926) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90dd27a270f0ce6e4d0133b979369f9d') in 0.3529868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.892841 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF - Drop Shadow - 2 Pass.mat
  artifactKey: Guid(947a44964e53bf0448ff698b2a2219c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF - Drop Shadow - 2 Pass.mat using Guid(947a44964e53bf0448ff698b2a2219c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de90d16e99dc1264bde0dc4884828c0c') in 0.0471757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF Glow.mat
  artifactKey: Guid(d75b8f41e959450c84ac6e967084d3e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF Glow.mat using Guid(d75b8f41e959450c84ac6e967084d3e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be8f5e1c196e26ab1b9179d5986a02a8') in 0.0599593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF Logo.mat
  artifactKey: Guid(f4e195ac1e204eff960149d1cb34e18c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF Logo.mat using Guid(f4e195ac1e204eff960149d1cb34e18c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa8ea24eb7c16635eddf6a9288b03822') in 0.0124344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.416179 seconds.
  path: Assets/Samples/XR Hands/1.5.1/HandVisualizer/Materials/Blue.mat
  artifactKey: Guid(7b26add94f09dc44fb811013f007a3aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Hands/1.5.1/HandVisualizer/Materials/Blue.mat using Guid(7b26add94f09dc44fb811013f007a3aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34acab3d2d7bfdd07bf803f5ce2e660f') in 0.011481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.200164 seconds.
  path: Assets/Material/RoomMaterials/Ceiling.mat
  artifactKey: Guid(2ff070698fe574d48b16d992cd83fcb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/RoomMaterials/Ceiling.mat using Guid(2ff070698fe574d48b16d992cd83fcb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8bd896b9016735d251fbfb62fd4b85d0') in 0.0218312 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.152127 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Materials/Concrete Dark Blue.mat
  artifactKey: Guid(bbb56ac3cf3c61a46ab3887c0fdbda8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Materials/Concrete Dark Blue.mat using Guid(bbb56ac3cf3c61a46ab3887c0fdbda8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2eac11f6004a45217668cb6f3cee5e3f') in 1.6061638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Materials/Concrete Grey.mat
  artifactKey: Guid(842f1b88643f1bb458ba6243088e344e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Materials/Concrete Grey.mat using Guid(842f1b88643f1bb458ba6243088e344e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab0c40ecb1ab604a1eddf6b0fcd54207') in 0.0150942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Materials/Concrete Light Blue.mat
  artifactKey: Guid(830d28b607e09a2479e2005c2eb5c75e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Materials/Concrete Light Blue.mat using Guid(830d28b607e09a2479e2005c2eb5c75e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '452b0d410b817608cbb2282f9df7918e') in 0.0155371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/Materials/Flat Blue.mat
  artifactKey: Guid(91ff3830fc4055a4fb0d0d2be32101a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/Materials/Flat Blue.mat using Guid(91ff3830fc4055a4fb0d0d2be32101a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77266674190b6f2783944d1a8738a82f') in 0.0149307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Material/RoomMaterials/Floor.mat
  artifactKey: Guid(9499ff438a95ab44aa8d873b831a7e8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/RoomMaterials/Floor.mat using Guid(9499ff438a95ab44aa8d873b831a7e8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '826bae80b5ef66c1bd1781af3b3b0e87') in 0.0449955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.352701 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans.ttf
  artifactKey: Guid(e3265ab4bf004d28a9537516768c1c75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans.ttf using Guid(e3265ab4bf004d28a9537516768c1c75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8501a5aea98da5ca596f753fcecac1a5') in 0.0242291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 2.373502 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Materials/Ground - Surface Shader Scene.mat
  artifactKey: Guid(aadd5a709a48466c887296bb5b1b8110) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Materials/Ground - Surface Shader Scene.mat using Guid(aadd5a709a48466c887296bb5b1b8110) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '224890443e74ddb78ff8cd4762cb3572') in 0.0231722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat
  artifactKey: Guid(79459efec17a4d00a321bdcc27bbc385) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat using Guid(79459efec17a4d00a321bdcc27bbc385) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0cd2bf07549b2abf937b63c467038f54') in 0.0243724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0