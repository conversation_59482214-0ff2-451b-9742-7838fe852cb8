Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.40f1 (157d81624ddf) revision 1408385'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'de' Physical Memory: 32508 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.40f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
-logFile
Logs/AssetImportWorker4.log
-srvPort
57088
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [39872]  Target information:

Player connection [39872]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1506197519 [EditorId] 1506197519 [Version] 1048832 [Id] WindowsEditor(7,TobiPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [39872] Host joined multi-casting on [***********:54997]...
Player connection [39872] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 640.31 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.40f1 (157d81624ddf)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/UnityGames/DigitalerZwillingOTH/DigitalerZwillingOTH/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 (ID=0x2216)
    Vendor:   NVIDIA
    VRAM:     10053 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56080
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.40f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.250309 seconds.
- Loaded All Assemblies, in  7.062 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.215 seconds
Domain Reload Profiling: 7275ms
	BeginReloadAssembly (6283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (169ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (283ms)
	LoadAllAssembliesAndSetupDomain (319ms)
		LoadAssemblies (6280ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (316ms)
				TypeCache.ScanAssembly (307ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (215ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (187ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (100ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  7.306 seconds
Refreshing native plugins compatible for Editor in 4.94 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 8083ms
	BeginReloadAssembly (107ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (18ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (7142ms)
		LoadAssemblies (6847ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (360ms)
			TypeCache.Refresh (312ms)
				TypeCache.ScanAssembly (297ms)
			BuildScriptInfoCaches (36ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 7.63 ms, found 10 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 233 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6948 unused Assets / (3.6 MB). Loaded Objects now: 7645.
Memory consumption went from 186.3 MB to 182.7 MB.
Total: 7.293500 ms (FindLiveObjects: 0.855000 ms CreateObjectMapping: 0.348300 ms MarkObjects: 4.372200 ms  DeleteObjects: 1.716900 ms)

========================================================================
Received Import Request.
  Time since last request: 353959.442885 seconds.
  path: Assets/Models/rober-h/source/habeck
  artifactKey: Guid(bf3aab43ac091ba479eddb6a4a83de18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/source/habeck using Guid(bf3aab43ac091ba479eddb6a4a83de18) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bbcb778b1846366acef848e28b56b020') in 0.0046085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.473463 seconds.
  path: Assets/Models/rober-h/source/habeck/avatar
  artifactKey: Guid(99f34dcdf96e8a14fa5ef839b8fed23e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/source/habeck/avatar using Guid(99f34dcdf96e8a14fa5ef839b8fed23e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9283ac7902cf51ec63db224230dbfee4') in 0.0003642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.021881 seconds.
  path: Assets/Models/rober-h/source/habeck/avatar/model.fbx
  artifactKey: Guid(cae79b1558431014a8a4edfa08d0fa9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/rober-h/source/habeck/avatar/model.fbx using Guid(cae79b1558431014a8a4edfa08d0fa9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa5eca5c319c99af64850147c4e1a01e') in 0.4548794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 200

========================================================================
Received Import Request.
  Time since last request: 98.453336 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Models/PushButton.fbx
  artifactKey: Guid(7ab6f3b0fd1a6ba41b2a47766c16613f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/DemoSceneAssets/Models/PushButton.fbx using Guid(7ab6f3b0fd1a6ba41b2a47766c16613f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae31a5a4a73a3529f234e546cc409d7a') in 0.0616734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Anton SDF - Sunny Days.mat
  artifactKey: Guid(6522f30e342599e4e9dd4cc2cc03c830) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Anton SDF - Sunny Days.mat using Guid(6522f30e342599e4e9dd4cc2cc03c830) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f3778bdca68a2a539bd6e3a6b0bf7f8e') in 0.1116537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Anton SDF - Outline.mat
  artifactKey: Guid(a00013af81304728b2be1f4309ee2433) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Anton SDF - Outline.mat using Guid(a00013af81304728b2be1f4309ee2433) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1cf9d2d1d9d351571b4ebf2af3a3e24b') in 0.0075349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 2.024937 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF - Drop Shadow.mat
  artifactKey: Guid(f2dcf029949142e28b974630369c8b4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF - Drop Shadow.mat using Guid(f2dcf029949142e28b974630369c8b4e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e3caa3f27c7080b38ff0fecf466eea39') in 0.0398417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF - Outline.mat
  artifactKey: Guid(f629c6e43dba4bf38cb74d8860150664) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF - Outline.mat using Guid(f629c6e43dba4bf38cb74d8860150664) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'add1e9ea52184c66c82be082d6c63b09') in 0.0186641 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF Logo - URP.mat
  artifactKey: Guid(6751ce1a4611b3940812c13a6847d94f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Bangers SDF Logo - URP.mat using Guid(6751ce1a4611b3940812c13a6847d94f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f94d22d1c1f739fbca38d37e420c80fa') in 1.9347262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Models/Rooms/single_window.obj
  artifactKey: Guid(467c580deacc44545ab80834fb79f399) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Rooms/single_window.obj using Guid(467c580deacc44545ab80834fb79f399) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9858808ea09440867c479b112010a3b7') in 0.0312392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/Materials/Controller_White.mat
  artifactKey: Guid(9f12d299d16099343a3c5c0d7285822a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/Materials/Controller_White.mat using Guid(9f12d299d16099343a3c5c0d7285822a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b25e231651917982aa72dd6b3108303') in 0.0375937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/Materials/Controller_Grey.mat
  artifactKey: Guid(99685157b02e4d446bbecb015645e5e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Samples/XR Interaction Toolkit/3.0.8/Starter Assets/Materials/Controller_Grey.mat using Guid(99685157b02e4d446bbecb015645e5e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3392eaa212f2058ee12ed164970dd3bf') in 0.0160598 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Materials/Crate - Surface Shader Scene.mat
  artifactKey: Guid(e6b9b44320f4448d9d5e0ee634259966) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Materials/Crate - Surface Shader Scene.mat using Guid(e6b9b44320f4448d9d5e0ee634259966) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '328fb64ccbd99c6a6e054e73a0e62d12') in 0.0356331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Materials/Crate - URP.mat
  artifactKey: Guid(3b5cc91c3bf8cf74391252247f52fb59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Materials/Crate - URP.mat using Guid(3b5cc91c3bf8cf74391252247f52fb59) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dd5583d5562b738c9bfa3e630edb3ba5') in 1.3645547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 1.949718 seconds.
  path: Assets/Material/RoomMaterials/Glass.mat
  artifactKey: Guid(91e9c411288c0b141b8b526bdc21e494) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material/RoomMaterials/Glass.mat using Guid(91e9c411288c0b141b8b526bdc21e494) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98185b189d0c710042ede04c8c5ec94f') in 0.0258599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat
  artifactKey: Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat using Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c241376f78884e41f093a52bf84391a2') in 0.0219861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/LiberationSans SDF - Metalic Green.mat
  artifactKey: Guid(8b29aaa3eec7468097ff07adfcf29ac9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/LiberationSans SDF - Metalic Green.mat using Guid(8b29aaa3eec7468097ff07adfcf29ac9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f328f422c09a6cb6dac7ae052bdfe297') in 0.090869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0