{"format": 1, "restore": {"H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {}}, "projects": {"H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets", "projectPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Temp\\obj\\Debug\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100-preview.6.24328.19\\RuntimeIdentifierGraph.json"}}}}}