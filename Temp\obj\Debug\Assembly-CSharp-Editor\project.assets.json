{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.Hands.Samples.VisualizerSample": "1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets": "1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "Unity.XR.Hands.Samples.VisualizerSample/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.Hands.Samples.VisualizerSample.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Hands.Samples.VisualizerSample.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.dll": {}}}}}, "libraries": {"Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "Unity.XR.Hands.Samples.VisualizerSample/1.0.0": {"type": "project", "path": "Unity.XR.Hands.Samples.VisualizerSample.csproj", "msbuildProject": "Unity.XR.Hands.Samples.VisualizerSample.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp >= 1.0.0", "Unity.XR.Hands.Samples.VisualizerSample >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Assembly-CSharp.csproj": {"projectPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Assembly-CSharp.csproj"}, "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Hands.Samples.VisualizerSample.csproj": {"projectPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Hands.Samples.VisualizerSample.csproj"}, "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"projectPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"projectPath": "H:\\UnityGames\\DigitalerZwillingOTH\\DigitalerZwillingOTH\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100-preview.6.24328.19\\RuntimeIdentifierGraph.json"}}}}